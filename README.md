# 智能红绿灯十字路口仿真系统

这是一个基于Web的十字路口交通仿真系统，模拟了红绿灯、机动车和行人的交通流动情况。

## 功能特点

### 🚦 红绿灯系统
- 南北和东西方向独立的红绿灯控制
- 绿灯30秒，黄灯3秒，红灯30秒的标准时序
- 实时显示红绿灯状态和倒计时

### 🚗 车辆仿真
- 四个方向（东、西、南、北）的车辆随机生成
- 车辆会在红灯和黄灯时停车等待
- 智能避让前方车辆，保持安全距离
- 不同颜色的车辆，带有方向指示箭头

### 🚶 行人仿真
- 四个人行横道的行人随机生成
- 行人只在对应方向红灯时通过（符合交通规则）
- 等待状态下的闪烁提示效果
- 行人图标显示

### 📊 统计功能
- 实时统计通过的车辆数量
- 实时统计通过的行人数量
- 计算平均等待时间
- 红绿灯状态实时显示

### 🎮 控制功能
- 开始/暂停/重置仿真
- 可调节仿真速度（0.5x - 3x）
- 响应式设计，支持移动设备

## 文件结构

```
智能红绿灯/
├── index.html          # 主页面文件
├── style.css           # 样式文件
├── script.js           # JavaScript逻辑文件
└── README.md           # 说明文档
```

## 使用方法

1. **启动仿真**
   - 在浏览器中打开 `index.html` 文件
   - 点击"开始仿真"按钮开始模拟

2. **控制仿真**
   - **开始仿真**: 启动交通流动模拟
   - **暂停**: 暂停当前仿真
   - **重置**: 清空所有车辆和行人，重置统计数据
   - **速度调节**: 使用滑块调整仿真速度

3. **观察仿真**
   - 观察红绿灯的变化周期
   - 查看车辆如何响应红绿灯信号
   - 注意行人如何在安全时机通过马路
   - 监控右侧的统计信息面板

## 技术实现

### 核心类

1. **TrafficSimulation**: 主仿真控制类
   - 管理整个仿真循环
   - 控制红绿灯时序
   - 生成车辆和行人
   - 更新UI显示

2. **Vehicle**: 车辆类
   - 处理车辆移动逻辑
   - 红绿灯停车判断
   - 车辆间距离检测
   - 车辆渲染

3. **Pedestrian**: 行人类
   - 行人过马路逻辑
   - 等待红灯判断
   - 行人动画效果
   - 行人渲染

### 关键算法

- **红绿灯时序控制**: 基于定时器的状态机
- **碰撞检测**: 简单的距离检测算法
- **路径规划**: 直线移动路径
- **随机生成**: 基于概率的车辆和行人生成

## 自定义配置

可以通过修改 `script.js` 中的参数来调整仿真行为：

```javascript
// 红绿灯时间设置
this.trafficLights = {
    ns: { state: 'green', timer: 30, maxTime: 30 },
    ew: { state: 'red', timer: 30, maxTime: 30 }
};

// 生成频率设置
if (this.vehicleSpawnInterval >= 60) { // 车辆生成间隔
if (this.pedestrianSpawnInterval >= 80) { // 行人生成间隔

// 速度设置
this.speed = 2; // 车辆速度
this.speed = 1; // 行人速度
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 未来改进方向

- [ ] 添加智能红绿灯算法（基于车流量调整时间）
- [ ] 增加更多车辆类型（公交车、卡车等）
- [ ] 添加转弯车辆支持
- [ ] 实现更复杂的路口布局
- [ ] 添加声音效果
- [ ] 数据导出功能
- [ ] 多路口联动仿真

## 许可证

MIT License
