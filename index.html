<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能红绿灯十字路口仿真</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="container">
        <h1>智能红绿灯十字路口仿真</h1>
        
        <div class="controls">
            <button id="startBtn">开始仿真</button>
            <button id="pauseBtn">暂停</button>
            <button id="resetBtn">重置</button>
            <div class="speed-control">
                <label for="speedSlider">仿真速度:</label>
                <input type="range" id="speedSlider" min="0.5" max="3" step="0.5" value="1">
                <span id="speedValue">1x</span>
            </div>
        </div>

        <div class="intersection-container">
            <canvas id="intersectionCanvas" width="800" height="600"></canvas>
        </div>

        <div class="info-panel">
            <div class="traffic-lights-status">
                <h3>红绿灯状态</h3>
                <div class="light-status">
                    <div class="direction">
                        <span>南北方向:</span>
                        <div class="light-indicator" id="nsLight"></div>
                        <span id="nsTimer">30s</span>
                    </div>
                    <div class="direction">
                        <span>东西方向:</span>
                        <div class="light-indicator" id="ewLight"></div>
                        <span id="ewTimer">30s</span>
                    </div>
                </div>
            </div>
            
            <div class="statistics">
                <h3>统计信息</h3>
                <div class="stat-item">
                    <span>通过车辆数:</span>
                    <span id="carCount">0</span>
                </div>
                <div class="stat-item">
                    <span>通过行人数:</span>
                    <span id="pedestrianCount">0</span>
                </div>
                <div class="stat-item">
                    <span>平均等待时间:</span>
                    <span id="avgWaitTime">0s</span>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
