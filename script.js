class TrafficSimulation {
    constructor() {
        this.canvas = document.getElementById('intersectionCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.isRunning = false;
        this.speed = 1;
        
        // 交通灯状态
        this.trafficLights = {
            ns: { state: 'green', timer: 30, maxTime: 30 }, // 南北方向
            ew: { state: 'red', timer: 30, maxTime: 30 }    // 东西方向
        };
        
        // 车辆和行人数组
        this.vehicles = [];
        this.pedestrians = [];
        
        // 统计数据
        this.stats = {
            carsPassed: 0,
            pedestriansPassed: 0,
            totalWaitTime: 0,
            waitingVehicles: 0
        };
        
        // 生成间隔
        this.vehicleSpawnInterval = 0;
        this.pedestrianSpawnInterval = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.draw();
        this.updateUI();
    }
    
    setupEventListeners() {
        document.getElementById('startBtn').addEventListener('click', () => this.start());
        document.getElementById('pauseBtn').addEventListener('click', () => this.pause());
        document.getElementById('resetBtn').addEventListener('click', () => this.reset());
        
        const speedSlider = document.getElementById('speedSlider');
        speedSlider.addEventListener('input', (e) => {
            this.speed = parseFloat(e.target.value);
            document.getElementById('speedValue').textContent = this.speed + 'x';
        });
    }
    
    start() {
        if (!this.isRunning) {
            this.isRunning = true;
            this.gameLoop();
        }
    }
    
    pause() {
        this.isRunning = false;
    }
    
    reset() {
        this.isRunning = false;
        this.vehicles = [];
        this.pedestrians = [];
        this.trafficLights.ns = { state: 'green', timer: 30, maxTime: 30 };
        this.trafficLights.ew = { state: 'red', timer: 30, maxTime: 30 };
        this.stats = {
            carsPassed: 0,
            pedestriansPassed: 0,
            totalWaitTime: 0,
            waitingVehicles: 0
        };
        this.draw();
        this.updateUI();
    }
    
    gameLoop() {
        if (!this.isRunning) return;
        
        this.update();
        this.draw();
        this.updateUI();
        
        setTimeout(() => this.gameLoop(), 100 / this.speed);
    }
    
    update() {
        this.updateTrafficLights();
        this.spawnVehicles();
        this.spawnPedestrians();
        this.updateVehicles();
        this.updatePedestrians();
    }
    
    updateTrafficLights() {
        // 更新南北方向红绿灯
        this.trafficLights.ns.timer -= 0.1 * this.speed;
        this.trafficLights.ew.timer -= 0.1 * this.speed;
        
        if (this.trafficLights.ns.timer <= 0) {
            this.switchTrafficLights();
        }
    }
    
    switchTrafficLights() {
        if (this.trafficLights.ns.state === 'green') {
            this.trafficLights.ns.state = 'yellow';
            this.trafficLights.ns.timer = 3;
        } else if (this.trafficLights.ns.state === 'yellow') {
            this.trafficLights.ns.state = 'red';
            this.trafficLights.ew.state = 'green';
            this.trafficLights.ns.timer = 30;
            this.trafficLights.ew.timer = 30;
        } else if (this.trafficLights.ns.state === 'red') {
            this.trafficLights.ew.state = 'yellow';
            this.trafficLights.ew.timer = 3;
        } else if (this.trafficLights.ew.state === 'yellow') {
            this.trafficLights.ns.state = 'green';
            this.trafficLights.ew.state = 'red';
            this.trafficLights.ns.timer = 30;
            this.trafficLights.ew.timer = 30;
        }
    }
    
    spawnVehicles() {
        this.vehicleSpawnInterval += this.speed;
        if (this.vehicleSpawnInterval >= 60) { // 每60帧生成一辆车
            this.vehicleSpawnInterval = 0;
            const directions = ['north', 'south', 'east', 'west'];
            const direction = directions[Math.floor(Math.random() * directions.length)];
            this.vehicles.push(new Vehicle(direction));
        }
    }
    
    spawnPedestrians() {
        this.pedestrianSpawnInterval += this.speed;
        if (this.pedestrianSpawnInterval >= 80) { // 每80帧生成一个行人
            this.pedestrianSpawnInterval = 0;
            const crossings = ['ns-east', 'ns-west', 'ew-north', 'ew-south'];
            const crossing = crossings[Math.floor(Math.random() * crossings.length)];
            this.pedestrians.push(new Pedestrian(crossing));
        }
    }
    
    updateVehicles() {
        for (let i = this.vehicles.length - 1; i >= 0; i--) {
            const vehicle = this.vehicles[i];
            vehicle.update(this.trafficLights, this.vehicles);
            
            if (vehicle.isOffScreen()) {
                this.vehicles.splice(i, 1);
                this.stats.carsPassed++;
            }
        }
    }
    
    updatePedestrians() {
        for (let i = this.pedestrians.length - 1; i >= 0; i--) {
            const pedestrian = this.pedestrians[i];
            pedestrian.update(this.trafficLights);

            if (pedestrian.isFinished()) {
                this.pedestrians.splice(i, 1);
                this.stats.pedestriansPassed++;
            }
        }
    }

    draw() {
        // 清空画布
        this.ctx.fillStyle = '#34495e';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制道路
        this.drawRoads();

        // 绘制红绿灯
        this.drawTrafficLights();

        // 绘制车辆
        this.vehicles.forEach(vehicle => vehicle.draw(this.ctx));

        // 绘制行人
        this.pedestrians.forEach(pedestrian => pedestrian.draw(this.ctx));

        // 绘制人行横道
        this.drawCrosswalks();
    }

    drawRoads() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const roadWidth = 120;

        // 绘制水平道路
        this.ctx.fillStyle = '#2c3e50';
        this.ctx.fillRect(0, centerY - roadWidth/2, this.canvas.width, roadWidth);

        // 绘制垂直道路
        this.ctx.fillRect(centerX - roadWidth/2, 0, roadWidth, this.canvas.height);

        // 绘制道路分隔线
        this.ctx.strokeStyle = '#f1c40f';
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([10, 10]);

        // 水平分隔线
        this.ctx.beginPath();
        this.ctx.moveTo(0, centerY);
        this.ctx.lineTo(centerX - roadWidth/2, centerY);
        this.ctx.moveTo(centerX + roadWidth/2, centerY);
        this.ctx.lineTo(this.canvas.width, centerY);
        this.ctx.stroke();

        // 垂直分隔线
        this.ctx.beginPath();
        this.ctx.moveTo(centerX, 0);
        this.ctx.lineTo(centerX, centerY - roadWidth/2);
        this.ctx.moveTo(centerX, centerY + roadWidth/2);
        this.ctx.lineTo(centerX, this.canvas.height);
        this.ctx.stroke();

        this.ctx.setLineDash([]);
    }

    drawTrafficLights() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const lightSize = 15;
        const offset = 80;

        // 南北方向红绿灯 (左右两侧)
        this.drawLight(centerX - offset, centerY - 20, this.trafficLights.ns.state);
        this.drawLight(centerX + offset, centerY + 20, this.trafficLights.ns.state);

        // 东西方向红绿灯 (上下两侧)
        this.drawLight(centerX - 20, centerY - offset, this.trafficLights.ew.state);
        this.drawLight(centerX + 20, centerY + offset, this.trafficLights.ew.state);
    }

    drawLight(x, y, state) {
        const lightSize = 12;

        // 红绿灯背景
        this.ctx.fillStyle = '#1a1a1a';
        this.ctx.fillRect(x - lightSize, y - lightSize*1.5, lightSize*2, lightSize*3);

        // 红灯
        this.ctx.fillStyle = state === 'red' ? '#e74c3c' : '#4a4a4a';
        this.ctx.beginPath();
        this.ctx.arc(x, y - lightSize, lightSize/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 黄灯
        this.ctx.fillStyle = state === 'yellow' ? '#f1c40f' : '#4a4a4a';
        this.ctx.beginPath();
        this.ctx.arc(x, y, lightSize/2, 0, Math.PI * 2);
        this.ctx.fill();

        // 绿灯
        this.ctx.fillStyle = state === 'green' ? '#27ae60' : '#4a4a4a';
        this.ctx.beginPath();
        this.ctx.arc(x, y + lightSize, lightSize/2, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawCrosswalks() {
        const centerX = this.canvas.width / 2;
        const centerY = this.canvas.height / 2;
        const roadWidth = 120;
        const stripeWidth = 8;

        this.ctx.fillStyle = '#ecf0f1';

        // 水平人行横道
        for (let i = 0; i < 15; i++) {
            const x = centerX - roadWidth/2 + i * stripeWidth * 2;
            this.ctx.fillRect(x, centerY - roadWidth/2 - 20, stripeWidth, 20);
            this.ctx.fillRect(x, centerY + roadWidth/2, stripeWidth, 20);
        }

        // 垂直人行横道
        for (let i = 0; i < 15; i++) {
            const y = centerY - roadWidth/2 + i * stripeWidth * 2;
            this.ctx.fillRect(centerX - roadWidth/2 - 20, y, 20, stripeWidth);
            this.ctx.fillRect(centerX + roadWidth/2, y, 20, stripeWidth);
        }
    }

    updateUI() {
        // 更新红绿灯状态显示
        const nsLight = document.getElementById('nsLight');
        const ewLight = document.getElementById('ewLight');

        nsLight.className = 'light-indicator ' + this.trafficLights.ns.state;
        ewLight.className = 'light-indicator ' + this.trafficLights.ew.state;

        document.getElementById('nsTimer').textContent = Math.ceil(this.trafficLights.ns.timer) + 's';
        document.getElementById('ewTimer').textContent = Math.ceil(this.trafficLights.ew.timer) + 's';

        // 更新统计信息
        document.getElementById('carCount').textContent = this.stats.carsPassed;
        document.getElementById('pedestrianCount').textContent = this.stats.pedestriansPassed;

        const avgWait = this.stats.carsPassed > 0 ?
            Math.round(this.stats.totalWaitTime / this.stats.carsPassed) : 0;
        document.getElementById('avgWaitTime').textContent = avgWait + 's';
    }
}

class Vehicle {
    constructor(direction) {
        this.direction = direction;
        this.speed = 2;
        this.width = 30;
        this.height = 15;
        this.color = this.getRandomColor();
        this.waitTime = 0;
        this.isStopped = false;

        // 根据方向设置初始位置
        this.setInitialPosition();
    }

    setInitialPosition() {
        const centerX = 400; // canvas width / 2
        const centerY = 300; // canvas height / 2
        const roadWidth = 120;

        switch (this.direction) {
            case 'north':
                this.x = centerX + 15;
                this.y = 600;
                this.targetY = centerY + roadWidth/2;
                break;
            case 'south':
                this.x = centerX - 15;
                this.y = 0;
                this.targetY = centerY - roadWidth/2;
                break;
            case 'east':
                this.x = 0;
                this.y = centerY + 15;
                this.targetX = centerX - roadWidth/2;
                break;
            case 'west':
                this.x = 800;
                this.y = centerY - 15;
                this.targetX = centerX + roadWidth/2;
                break;
        }
    }

    getRandomColor() {
        const colors = ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    update(trafficLights, allVehicles) {
        const centerX = 400;
        const centerY = 300;
        const stopDistance = 40;

        this.isStopped = false;

        // 检查是否需要在红绿灯前停车
        if (this.shouldStopAtLight(trafficLights, centerX, centerY, stopDistance)) {
            this.isStopped = true;
            this.waitTime++;
            return;
        }

        // 检查前方是否有车辆
        if (this.hasVehicleAhead(allVehicles)) {
            this.isStopped = true;
            return;
        }

        // 移动车辆
        this.move();
    }

    shouldStopAtLight(trafficLights, centerX, centerY, stopDistance) {
        switch (this.direction) {
            case 'north':
                return this.y > centerY + stopDistance &&
                       trafficLights.ns.state !== 'green';
            case 'south':
                return this.y < centerY - stopDistance &&
                       trafficLights.ns.state !== 'green';
            case 'east':
                return this.x < centerX - stopDistance &&
                       trafficLights.ew.state !== 'green';
            case 'west':
                return this.x > centerX + stopDistance &&
                       trafficLights.ew.state !== 'green';
        }
        return false;
    }

    hasVehicleAhead(allVehicles) {
        const safeDistance = 40;

        for (let vehicle of allVehicles) {
            if (vehicle === this) continue;

            switch (this.direction) {
                case 'north':
                    if (Math.abs(vehicle.x - this.x) < 20 &&
                        vehicle.y < this.y && vehicle.y > this.y - safeDistance) {
                        return true;
                    }
                    break;
                case 'south':
                    if (Math.abs(vehicle.x - this.x) < 20 &&
                        vehicle.y > this.y && vehicle.y < this.y + safeDistance) {
                        return true;
                    }
                    break;
                case 'east':
                    if (Math.abs(vehicle.y - this.y) < 20 &&
                        vehicle.x > this.x && vehicle.x < this.x + safeDistance) {
                        return true;
                    }
                    break;
                case 'west':
                    if (Math.abs(vehicle.y - this.y) < 20 &&
                        vehicle.x < this.x && vehicle.x > this.x - safeDistance) {
                        return true;
                    }
                    break;
            }
        }
        return false;
    }

    move() {
        switch (this.direction) {
            case 'north':
                this.y -= this.speed;
                break;
            case 'south':
                this.y += this.speed;
                break;
            case 'east':
                this.x += this.speed;
                break;
            case 'west':
                this.x -= this.speed;
                break;
        }
    }

    draw(ctx) {
        ctx.fillStyle = this.color;

        if (this.direction === 'north' || this.direction === 'south') {
            ctx.fillRect(this.x - this.width/2, this.y - this.height/2, this.width, this.height);
        } else {
            ctx.fillRect(this.x - this.height/2, this.y - this.width/2, this.height, this.width);
        }

        // 绘制车辆方向指示
        ctx.fillStyle = '#ffffff';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';

        const arrow = this.getDirectionArrow();
        ctx.fillText(arrow, this.x, this.y + 4);
    }

    getDirectionArrow() {
        switch (this.direction) {
            case 'north': return '↑';
            case 'south': return '↓';
            case 'east': return '→';
            case 'west': return '←';
            default: return '•';
        }
    }

    isOffScreen() {
        return this.x < -50 || this.x > 850 || this.y < -50 || this.y > 650;
    }
}

class Pedestrian {
    constructor(crossing) {
        this.crossing = crossing; // 'ns-east', 'ns-west', 'ew-north', 'ew-south'
        this.speed = 1;
        this.size = 8;
        this.color = '#e67e22';
        this.waitTime = 0;
        this.isWaiting = true;
        this.progress = 0;

        this.setInitialPosition();
    }

    setInitialPosition() {
        const centerX = 400;
        const centerY = 300;
        const roadWidth = 120;

        switch (this.crossing) {
            case 'ns-east': // 从东侧穿越南北道路
                this.startX = centerX + roadWidth/2 + 20;
                this.startY = centerY;
                this.endX = centerX - roadWidth/2 - 20;
                this.endY = centerY;
                break;
            case 'ns-west': // 从西侧穿越南北道路
                this.startX = centerX - roadWidth/2 - 20;
                this.startY = centerY;
                this.endX = centerX + roadWidth/2 + 20;
                this.endY = centerY;
                break;
            case 'ew-north': // 从北侧穿越东西道路
                this.startX = centerX;
                this.startY = centerY - roadWidth/2 - 20;
                this.endX = centerX;
                this.endY = centerY + roadWidth/2 + 20;
                break;
            case 'ew-south': // 从南侧穿越东西道路
                this.startX = centerX;
                this.startY = centerY + roadWidth/2 + 20;
                this.endX = centerX;
                this.endY = centerY - roadWidth/2 - 20;
                break;
        }

        this.x = this.startX;
        this.y = this.startY;
    }

    update(trafficLights) {
        if (this.isWaiting) {
            // 检查是否可以开始过马路
            if (this.canCross(trafficLights)) {
                this.isWaiting = false;
            } else {
                this.waitTime++;
                return;
            }
        }

        // 移动行人
        this.progress += this.speed / 100;
        if (this.progress >= 1) {
            this.progress = 1;
        }

        this.x = this.startX + (this.endX - this.startX) * this.progress;
        this.y = this.startY + (this.endY - this.startY) * this.progress;
    }

    canCross(trafficLights) {
        // 行人在车辆红灯时可以通过
        if (this.crossing.startsWith('ns')) {
            return trafficLights.ns.state === 'red';
        } else {
            return trafficLights.ew.state === 'red';
        }
    }

    draw(ctx) {
        if (this.isWaiting) {
            // 等待状态下闪烁显示
            if (Math.floor(Date.now() / 500) % 2) {
                ctx.fillStyle = this.color;
            } else {
                ctx.fillStyle = '#d35400';
            }
        } else {
            ctx.fillStyle = this.color;
        }

        // 绘制行人（圆形）
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
        ctx.fill();

        // 绘制行人标识
        ctx.fillStyle = '#ffffff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('👤', this.x, this.y + 3);
    }

    isFinished() {
        return this.progress >= 1;
    }
}

// 初始化仿真
let simulation;
window.addEventListener('load', () => {
    simulation = new TrafficSimulation();
});
