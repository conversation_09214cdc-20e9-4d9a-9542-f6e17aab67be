* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f0f0f0;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 2.5em;
}

.controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

button {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

#startBtn {
    background-color: #27ae60;
    color: white;
}

#startBtn:hover {
    background-color: #229954;
}

#pauseBtn {
    background-color: #f39c12;
    color: white;
}

#pauseBtn:hover {
    background-color: #e67e22;
}

#resetBtn {
    background-color: #e74c3c;
    color: white;
}

#resetBtn:hover {
    background-color: #c0392b;
}

.speed-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

#speedSlider {
    width: 100px;
}

.intersection-container {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#intersectionCanvas {
    border: 2px solid #34495e;
    border-radius: 10px;
    background-color: #2c3e50;
    box-shadow: 0 4px 20px rgba(0,0,0,0.2);
}

.info-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.traffic-lights-status, .statistics {
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.traffic-lights-status h3, .statistics h3 {
    margin-bottom: 15px;
    color: #2c3e50;
    border-bottom: 2px solid #3498db;
    padding-bottom: 5px;
}

.light-status {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.direction {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 16px;
}

.light-indicator {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #333;
    transition: all 0.3s ease;
}

.light-indicator.red {
    background-color: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.light-indicator.yellow {
    background-color: #f1c40f;
    box-shadow: 0 0 10px rgba(241, 196, 15, 0.5);
}

.light-indicator.green {
    background-color: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px;
    background-color: #ecf0f1;
    border-radius: 5px;
}

.stat-item span:last-child {
    font-weight: bold;
    color: #2c3e50;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    .controls {
        flex-wrap: wrap;
    }
    
    #intersectionCanvas {
        width: 100%;
        height: auto;
    }
    
    .info-panel {
        grid-template-columns: 1fr;
    }
}
